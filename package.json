{"name": "reedsoft-monorepo", "version": "1.0.0", "description": "Reedsoft startup monorepo with React frontend, Python backend, and mobile app", "private": true, "workspaces": ["frontend"], "scripts": {"dev": "npm run dev --workspace=frontend", "build": "npm run build --workspace=frontend", "test": "npm run test --workspace=frontend", "start:frontend": "npm run start --workspace=frontend", "build:frontend": "npm run build --workspace=frontend", "test:frontend": "npm run test --workspace=frontend", "lint": "npm run lint --workspace=frontend", "clean": "rm -rf node_modules frontend/node_modules", "install:all": "npm install && npm install --workspace=frontend"}, "keywords": ["monorepo", "react", "typescript", "python", "reedsoft", "japan-preparation"], "author": "Reedsoft", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}